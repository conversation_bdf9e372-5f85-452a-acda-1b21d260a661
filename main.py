#!/usr/bin/env python3
"""
Main entry point for Chain Snatching Theft Detection System
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

def main():
    print("Chain Snatching Theft Detection System")
    print("=" * 40)
    print("1. Train model: python src/train_three_class_model.py")
    print("2. Detect in video: python src/advanced_theft_detector.py --video input.mp4 --model models/runs/train/*/weights/best.pt")
    print("3. Web interface: python src/web_interface.py")
    print("4. Update dataset: python src/update_dataset_classes.py")

if __name__ == "__main__":
    main()
