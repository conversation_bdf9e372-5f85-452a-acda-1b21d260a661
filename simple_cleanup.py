#!/usr/bin/env python3
"""
Simple cleanup script to remove unwanted files and organize the project
"""

import os
import shutil
from pathlib import Path

def cleanup_unwanted_files():
    """Remove unwanted files and directories"""
    
    print("=== Cleaning up unwanted files ===")
    
    # Files to remove
    files_to_remove = [
        "execute.py",           # Old hardcoded script
        "training.py",          # Old Colab-specific script
        "train_model.py",       # Old training script
        "simple_train.py",      # Simplified version no longer needed
        "test_model.py",        # Old testing script
        "combine_datasets.py",  # One-time use script
        "inference.py",         # Old inference script
        "yolov8n.pt",          # Downloaded model (will re-download)
        "project report.pdf",   # Old report
        "iit hyd refernce idea.pdf",  # Reference file
    ]
    
    # Directories to remove
    dirs_to_remove = [
        "combined_dataset",     # Old dataset structure
        "dataset_v1",          # Temporary extraction
        "dataset_v9",          # Temporary extraction
        "test_results",        # Old test outputs
        "__pycache__",         # Python cache
        "output_videos",       # Old outputs
    ]
    
    # Remove files
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ Removed: {file_path}")
            except Exception as e:
                print(f"❌ Failed to remove {file_path}: {e}")
        else:
            print(f"⚠️  Not found: {file_path}")
    
    # Remove directories
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"✅ Removed directory: {dir_path}")
            except Exception as e:
                print(f"❌ Failed to remove {dir_path}: {e}")
        else:
            print(f"⚠️  Directory not found: {dir_path}")
    
    # Remove ZIP files (keep only the latest version)
    zip_files = [f for f in os.listdir('.') if f.endswith('.zip')]
    if len(zip_files) > 1:
        # Keep only the latest version (v9)
        for zip_file in zip_files:
            if 'v9' not in zip_file:
                try:
                    os.remove(zip_file)
                    print(f"✅ Removed old ZIP: {zip_file}")
                except Exception as e:
                    print(f"❌ Failed to remove {zip_file}: {e}")

def organize_project():
    """Organize remaining files"""
    
    print("\n=== Organizing project structure ===")
    
    # Create clean directory structure
    directories = ["src", "models", "data", "docs"]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ Created: {dir_name}/")
    
    # Move files to appropriate directories
    file_moves = {
        # Source code
        "advanced_theft_detector.py": "src/",
        "train_three_class_model.py": "src/",
        "web_interface.py": "src/",
        
        # Data
        "updated_dataset": "data/",
        
        # Documentation
        "research_paper.md": "docs/",
        "FINAL_PROJECT_SUMMARY.md": "docs/",
        "PROJECT_SUMMARY.md": "docs/",
        "README_UPGRADED_SYSTEM.md": "docs/",
        "annotation_guide.md": "docs/",
        
        # Models
        "runs": "models/",
        
        # Scripts
        "update_dataset_classes.py": "src/",
        "cleanup_and_optimize.py": "src/",
    }
    
    for source, destination in file_moves.items():
        if os.path.exists(source):
            try:
                dest_path = Path(destination) / Path(source).name
                if Path(source).is_dir():
                    if dest_path.exists():
                        shutil.rmtree(dest_path)
                    shutil.move(source, destination)
                else:
                    if dest_path.exists():
                        dest_path.unlink()
                    shutil.move(source, dest_path)
                print(f"✅ Moved: {source} → {destination}")
            except Exception as e:
                print(f"❌ Failed to move {source}: {e}")

def create_main_script():
    """Create main entry point script"""
    
    print("\n=== Creating main.py ===")
    
    main_content = '''#!/usr/bin/env python3
"""
Main entry point for Chain Snatching Theft Detection System
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

def main():
    print("Chain Snatching Theft Detection System")
    print("=" * 40)
    print("1. Train model: python src/train_three_class_model.py")
    print("2. Detect in video: python src/advanced_theft_detector.py --video input.mp4 --model models/runs/train/*/weights/best.pt")
    print("3. Web interface: python src/web_interface.py")
    print("4. Update dataset: python src/update_dataset_classes.py")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("main.py", "w", encoding='utf-8') as f:
            f.write(main_content)
        print("✅ Created main.py")
    except Exception as e:
        print(f"❌ Failed to create main.py: {e}")

def create_requirements():
    """Create requirements.txt"""
    
    print("\n=== Creating requirements.txt ===")
    
    requirements = [
        "ultralytics>=8.0.0",
        "opencv-python>=4.8.0", 
        "torch>=2.0.0",
        "numpy>=1.24.0",
        "matplotlib>=3.7.0",
        "pyyaml>=6.0",
        "pillow>=10.0.0",
        "pandas>=2.0.0"
    ]
    
    try:
        with open("requirements.txt", "w") as f:
            for req in requirements:
                f.write(f"{req}\n")
        print("✅ Created requirements.txt")
    except Exception as e:
        print(f"❌ Failed to create requirements.txt: {e}")

def main():
    """Main cleanup function"""
    
    print("🧹 SIMPLE CLEANUP SCRIPT")
    print("=" * 30)
    
    # Step 1: Cleanup unwanted files
    cleanup_unwanted_files()
    
    # Step 2: Organize project
    organize_project()
    
    # Step 3: Create main script
    create_main_script()
    
    # Step 4: Create requirements
    create_requirements()
    
    print("\n" + "=" * 30)
    print("✅ CLEANUP COMPLETE!")
    print("=" * 30)
    
    print("\nProject structure:")
    print("├── src/           # Source code")
    print("├── data/          # Dataset")
    print("├── models/        # Trained models")
    print("├── docs/          # Documentation")
    print("├── main.py        # Entry point")
    print("└── requirements.txt")
    
    print("\nNext steps:")
    print("1. python src/train_three_class_model.py  # Train model")
    print("2. python main.py                         # See usage options")

if __name__ == "__main__":
    main()
