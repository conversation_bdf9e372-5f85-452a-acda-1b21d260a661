#!/usr/bin/env python3
"""
Simple training script for Chain Snatching Theft Detection using YOLOv8
"""

import os
from ultralytics import YOLO
import yaml

def main():
    print("=== Simple Chain Snatching Theft Detection Training ===")
    
    # Check dataset
    data_path = "combined_dataset/data.yaml"
    if not os.path.exists(data_path):
        print(f"Error: Dataset configuration not found: {data_path}")
        return
    
    with open(data_path, 'r') as f:
        data = yaml.safe_load(f)
    
    print(f"Dataset: {data['nc']} classes - {data['names']}")
    
    # Load model
    model = YOLO('yolov8n.pt')  # Use nano model for small dataset
    
    # Simple training with reduced parameters
    try:
        print("Starting training...")
        results = model.train(
            data=data_path,
            epochs=50,  # Reduced epochs
            imgsz=416,  # Smaller image size
            batch=4,    # Small batch size
            patience=20,
            save=True,
            plots=True,
            device='cpu',  # Force CPU to avoid GPU memory issues
            workers=0,     # No multiprocessing
            verbose=True
        )
        
        print("Training completed successfully!")
        print(f"Results saved to: {results.save_dir}")
        
        # Validate the model
        print("Running validation...")
        val_results = model.val()
        print(f"mAP50: {val_results.box.map50:.4f}")
        print(f"mAP50-95: {val_results.box.map:.4f}")
        
    except Exception as e:
        print(f"Training failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
