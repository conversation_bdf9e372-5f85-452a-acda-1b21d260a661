#!/usr/bin/env python3
"""
Web interface for Chain Snatching Theft Detection
Upload video and get real-time frame-by-frame analysis
"""

import streamlit as st
import cv2
import tempfile
import os
from pathlib import Path
import json
from advanced_theft_detector import AdvancedTheftDetector
import time
import pandas as pd

# Page configuration
st.set_page_config(
    page_title="Chain Snatching Theft Detection",
    page_icon="🚨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    font-size: 3rem;
    color: #ff4b4b;
    text-align: center;
    margin-bottom: 2rem;
}
.alert-box {
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.alert-high {
    background-color: #ffebee;
    border-left: 5px solid #f44336;
}
.alert-medium {
    background-color: #fff3e0;
    border-left: 5px solid #ff9800;
}
.alert-low {
    background-color: #e8f5e8;
    border-left: 5px solid #4caf50;
}
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown('<h1 class="main-header">🚨 Chain Snatching Theft Detection System</h1>', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Model selection
        model_path = st.text_input(
            "Model Path", 
            value="runs/detect/train/weights/best.pt",
            help="Path to trained YOLOv8 model"
        )
        
        # Confidence threshold
        confidence = st.slider(
            "Confidence Threshold", 
            min_value=0.1, 
            max_value=1.0, 
            value=0.5, 
            step=0.05,
            help="Minimum confidence for detections"
        )
        
        # Processing options
        st.subheader("Processing Options")
        save_output = st.checkbox("Save Output Video", value=True)
        show_progress = st.checkbox("Show Progress", value=True)
        
        # Class information
        st.subheader("📋 Detection Classes")
        st.info("""
        **chain_snatch** 🔴: Theft action
        
        **running** 🔵: Fast movement
        
        **suspect** 🟡: Chain snatch + Running
        (HIGH ALERT)
        """)
    
    # Main content
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📹 Video Upload")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a video file",
            type=['mp4', 'avi', 'mov', 'mkv'],
            help="Upload surveillance video for theft detection"
        )
        
        if uploaded_file is not None:
            # Display video info
            st.success(f"✅ Video uploaded: {uploaded_file.name}")
            st.info(f"File size: {uploaded_file.size / (1024*1024):.2f} MB")
            
            # Process button
            if st.button("🚀 Start Detection", type="primary"):
                process_video(uploaded_file, model_path, confidence, save_output, show_progress)
    
    with col2:
        st.header("📊 System Status")
        
        # System checks
        check_system_status(model_path)
        
        # Recent results
        show_recent_results()

def check_system_status(model_path):
    """Check system status and model availability"""
    
    status_container = st.container()
    
    with status_container:
        # Model check
        if os.path.exists(model_path):
            st.success(f"✅ Model loaded: {Path(model_path).name}")
        else:
            st.error(f"❌ Model not found: {model_path}")
            st.info("Please train a model first or check the path")
        
        # Dependencies check
        try:
            import ultralytics
            st.success("✅ Ultralytics YOLO available")
        except ImportError:
            st.error("❌ Ultralytics not installed")
        
        try:
            import cv2
            st.success("✅ OpenCV available")
        except ImportError:
            st.error("❌ OpenCV not installed")

def process_video(uploaded_file, model_path, confidence, save_output, show_progress):
    """Process uploaded video for theft detection"""
    
    if not os.path.exists(model_path):
        st.error("❌ Model file not found! Please check the model path.")
        return
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
        tmp_file.write(uploaded_file.read())
        temp_video_path = tmp_file.name
    
    try:
        # Initialize detector
        with st.spinner("🔄 Initializing detection system..."):
            detector = AdvancedTheftDetector(model_path, confidence)
        
        # Create output path
        output_path = None
        if save_output:
            output_dir = Path("output_videos")
            output_dir.mkdir(exist_ok=True)
            output_path = output_dir / f"detected_{uploaded_file.name}"
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Results containers
        alerts_container = st.container()
        metrics_container = st.container()
        
        # Process video
        st.info("🎬 Processing video frame by frame...")
        
        # Get video info
        cap = cv2.VideoCapture(temp_video_path)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        cap.release()
        
        # Custom processing with progress updates
        alerts = process_with_progress(
            detector, temp_video_path, output_path, 
            progress_bar, status_text, total_frames
        )
        
        # Display results
        display_results(alerts, alerts_container, metrics_container, output_path)
        
    except Exception as e:
        st.error(f"❌ Processing failed: {str(e)}")
    
    finally:
        # Cleanup
        if os.path.exists(temp_video_path):
            os.unlink(temp_video_path)

def process_with_progress(detector, video_path, output_path, progress_bar, status_text, total_frames):
    """Process video with progress updates"""
    
    # Mock processing with progress (simplified for demo)
    # In real implementation, you'd modify the detector to yield progress
    
    alerts = []
    
    for frame_num in range(0, total_frames, max(1, total_frames // 100)):
        progress = frame_num / total_frames
        progress_bar.progress(progress)
        status_text.text(f"Processing frame {frame_num}/{total_frames}")
        time.sleep(0.01)  # Simulate processing time
    
    # Run actual detection
    status_text.text("Running detection algorithm...")
    alerts = detector.process_video(video_path, output_path)
    
    progress_bar.progress(1.0)
    status_text.text("✅ Processing complete!")
    
    return alerts

def display_results(alerts, alerts_container, metrics_container, output_path):
    """Display detection results"""
    
    with alerts_container:
        st.header("🚨 Detection Results")
        
        if alerts:
            st.error(f"⚠️ {len(alerts)} SECURITY ALERTS DETECTED!")
            
            # Display alerts
            for i, alert in enumerate(alerts, 1):
                severity_class = f"alert-{alert['severity'].lower()}"
                
                st.markdown(f"""
                <div class="alert-box {severity_class}">
                    <h4>Alert #{i}: {alert['type']}</h4>
                    <p><strong>Frame:</strong> {alert['frame']}</p>
                    <p><strong>Time:</strong> {alert['timestamp']}</p>
                    <p><strong>Severity:</strong> {alert['severity']}</p>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.success("✅ No security threats detected in the video")
    
    with metrics_container:
        st.header("📊 Analysis Metrics")
        
        # Create metrics dataframe
        if alerts:
            alert_types = [alert['type'] for alert in alerts]
            alert_frames = [alert['frame'] for alert in alerts]
            alert_severity = [alert['severity'] for alert in alerts]
            
            df = pd.DataFrame({
                'Alert Type': alert_types,
                'Frame': alert_frames,
                'Severity': alert_severity
            })
            
            st.dataframe(df)
            
            # Alert type distribution
            st.subheader("Alert Distribution")
            alert_counts = pd.Series(alert_types).value_counts()
            st.bar_chart(alert_counts)
        
        # Download options
        if output_path and os.path.exists(output_path):
            st.subheader("📥 Download Results")
            
            with open(output_path, 'rb') as f:
                st.download_button(
                    label="Download Processed Video",
                    data=f.read(),
                    file_name=f"detected_{int(time.time())}.mp4",
                    mime="video/mp4"
                )
        
        if alerts:
            # Download alerts as JSON
            alerts_json = json.dumps(alerts, indent=2)
            st.download_button(
                label="Download Alerts (JSON)",
                data=alerts_json,
                file_name=f"alerts_{int(time.time())}.json",
                mime="application/json"
            )

def show_recent_results():
    """Show recent detection results"""
    
    st.subheader("📈 Recent Activity")
    
    # Check for recent results
    output_dir = Path("output_videos")
    if output_dir.exists():
        recent_files = sorted(output_dir.glob("*.mp4"), key=os.path.getmtime, reverse=True)[:5]
        
        if recent_files:
            st.info(f"Found {len(recent_files)} recent processed videos")
            for file in recent_files:
                st.text(f"📹 {file.name}")
        else:
            st.info("No recent processed videos found")
    else:
        st.info("No processing history available")

# Instructions
with st.expander("📖 How to Use"):
    st.markdown("""
    ### Step-by-Step Guide:
    
    1. **Upload Video**: Click "Choose a video file" and select your surveillance video
    2. **Configure Settings**: Adjust confidence threshold and processing options in the sidebar
    3. **Start Detection**: Click "🚀 Start Detection" to begin frame-by-frame analysis
    4. **Review Results**: Check alerts and download processed video
    
    ### Detection Classes:
    - **chain_snatch** 🔴: Detects the actual theft action
    - **running** 🔵: Detects fast movement or escape behavior
    - **suspect** 🟡: HIGH ALERT - Combination of theft + running
    
    ### Alert System:
    - **HIGH**: Suspect confirmed (immediate security response needed)
    - **MEDIUM**: Individual theft or running detected
    - **LOW**: Normal activity
    """)

if __name__ == "__main__":
    main()
