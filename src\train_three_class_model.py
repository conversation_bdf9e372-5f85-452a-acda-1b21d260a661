#!/usr/bin/env python3
"""
Simple training script for three-class theft detection
"""

import os
from ultralytics import YOLO
import yaml

def main():
    print("=== Three-Class Theft Detection Training ===")

    # Use the best available dataset
    dataset_paths = [
        "final_dataset/data.yaml",
        "updated_dataset/data.yaml",
        "TheftDetection_Combined/data.yaml",
        "temp1/data.yaml"
    ]

    data_path = None
    for path in dataset_paths:
        if os.path.exists(path):
            data_path = path
            break

    if not data_path:
        print("❌ No dataset found!")
        return

    print(f"Using dataset: {data_path}")

    # Check dataset
    with open(data_path, 'r') as f:
        data = yaml.safe_load(f)

    print(f"Classes: {data.get('names', [])}")
    print(f"Number of classes: {data.get('nc', 0)}")

    # Load model
    model = YOLO('yolov8n.pt')

    # Train with simple parameters
    try:
        print("Starting training...")
        results = model.train(
            data=data_path,
            epochs=50,
            imgsz=640,
            batch=4,
            patience=20,
            save=True,
            plots=True,
            device='cpu',
            workers=0,
            verbose=True,
            project='runs/train',
            name='theft_detection'
        )

        print("✅ Training completed!")
        print(f"Results: {results.save_dir}")

        # Test the model
        val_results = model.val()
        print(f"mAP50: {val_results.box.map50:.4f}")

    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main(). chain_snatch - Theft action
2. running - Fast movement
3. suspect - Combination (HIGH PRIORITY)
"""

import os
import sys
from pathlib import Path
import yaml
import torch
from ultralytics import YOLO
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

class ThreeClassTrainer:
    def __init__(self, data_path, model_size='n'):
        """
        Initialize trainer for three-class system
        
        Args:
            data_path: Path to dataset YAML
            model_size: YOLOv8 model size (n, s, m, l, x)
        """
        self.data_path = data_path
        self.model_size = model_size
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        print(f"Three-Class Theft Detection Trainer")
        print(f"Device: {self.device}")
        print(f"Model: YOLOv8{model_size}")
    
    def validate_dataset(self):
        """Validate three-class dataset"""
        print("=== Validating Three-Class Dataset ===")
        
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"Dataset config not found: {self.data_path}")
        
        with open(self.data_path, 'r') as f:
            data = yaml.safe_load(f)
        
        # Validate classes
        expected_classes = ['chain_snatch', 'running', 'suspect']
        if data.get('names') != expected_classes:
            print(f"Warning: Expected classes {expected_classes}")
            print(f"Found classes: {data.get('names')}")
        
        # Check dataset splits
        base_dir = Path(self.data_path).parent
        stats = {}
        
        for split in ['train', 'val', 'test']:
            if split in data:
                split_path = base_dir / data[split]
                if split_path.exists():
                    img_count = len(list(split_path.glob('*.jpg'))) + len(list(split_path.glob('*.png')))
                    stats[split] = img_count
                    print(f"{split}: {img_count} images")
        
        print(f"Classes: {data['names']}")
        return data, stats
    
    def setup_training_config(self):
        """Setup optimized training configuration for three-class system"""
        
        # Class-specific weights (higher weight for suspect class)
        class_weights = {
            0: 1.0,  # chain_snatch
            1: 1.0,  # running  
            2: 2.0   # suspect (higher priority)
        }
        
        config = {
            'data': self.data_path,
            'epochs': 100,
            'imgsz': 640,
            'batch': 8 if self.device == 'cuda' else 4,
            'device': self.device,
            'project': 'runs/train',
            'name': f'three_class_theft_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'save': True,
            'save_period': 10,
            'plots': True,
            'val': True,
            'patience': 30,
            'optimizer': 'AdamW',
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            
            # Loss weights (emphasize suspect class)
            'box': 7.5,
            'cls': 1.0,  # Increased for better classification
            'dfl': 1.5,
            
            # Data augmentation
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 10.0,     # Slight rotation
            'translate': 0.1,
            'scale': 0.5,
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.1,        # Light mixup for better generalization
            'copy_paste': 0.1,   # Copy-paste augmentation
            
            # Advanced settings
            'label_smoothing': 0.1,
            'nbs': 64,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'verbose': True
        }
        
        return config
    
    def train_model(self):
        """Train the three-class model"""
        print("=== Starting Three-Class Model Training ===")
        
        # Validate dataset
        dataset_info, stats = self.validate_dataset()
        
        # Load model
        model_name = f"yolov8{self.model_size}.pt"
        print(f"Loading model: {model_name}")
        model = YOLO(model_name)
        
        # Get training configuration
        config = self.setup_training_config()
        
        print("Training Configuration:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # Start training
        try:
            print("\n🚀 Starting training...")
            results = model.train(**config)
            
            print("✅ Training completed successfully!")
            
            # Get results directory
            results_dir = Path(f"runs/train/{config['name']}")
            
            # Run validation
            print("\n📊 Running final validation...")
            val_results = model.val(data=self.data_path)
            
            # Print metrics
            self.print_results(val_results, results_dir)
            
            return results, results_dir
            
        except Exception as e:
            print(f"❌ Training failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def print_results(self, val_results, results_dir):
        """Print detailed results"""
        print("\n" + "="*60)
        print("TRAINING RESULTS - THREE-CLASS THEFT DETECTION")
        print("="*60)
        
        # Overall metrics
        print(f"Overall mAP50: {val_results.box.map50:.4f}")
        print(f"Overall mAP50-95: {val_results.box.map:.4f}")
        
        # Per-class metrics
        if hasattr(val_results.box, 'ap_class_index') and val_results.box.ap_class_index is not None:
            print("\nPer-Class Performance:")
            print("-" * 50)
            
            class_names = ['chain_snatch', 'running', 'suspect']
            
            for i, class_idx in enumerate(val_results.box.ap_class_index):
                if i < len(class_names):
                    class_name = class_names[i]
                    ap50 = val_results.box.ap50[i] if i < len(val_results.box.ap50) else 0
                    ap = val_results.box.ap[i] if i < len(val_results.box.ap) else 0
                    
                    # Special highlighting for suspect class
                    marker = "🚨" if class_name == 'suspect' else "📊"
                    print(f"  {marker} {class_name:12}: mAP50={ap50:.4f}, mAP50-95={ap:.4f}")
        
        # Model info
        best_model = results_dir / "weights" / "best.pt"
        if best_model.exists():
            print(f"\n✅ Best model saved: {best_model}")
            print(f"📁 Results directory: {results_dir}")
        
        print("\n" + "="*60)
    
    def create_deployment_script(self, model_path):
        """Create deployment script for the trained model"""
        
        deployment_script = f'''#!/usr/bin/env python3
"""
Quick deployment script for three-class theft detection
Generated automatically after training
"""

from advanced_theft_detector import AdvancedTheftDetector

def detect_theft_in_video(video_path, output_path=None):
    """
    Detect theft in video using trained three-class model
    
    Args:
        video_path: Path to input video
        output_path: Path to save output video (optional)
    """
    
    # Initialize detector with trained model
    detector = AdvancedTheftDetector(
        model_path="{model_path}",
        confidence_threshold=0.5
    )
    
    # Process video
    alerts = detector.process_video(video_path, output_path)
    
    # Print summary
    if alerts:
        print(f"🚨 SECURITY ALERT: {{len(alerts)}} threats detected!")
        for alert in alerts:
            print(f"  - {{alert['type']}} at frame {{alert['frame']}}")
    else:
        print("✅ No threats detected")
    
    return alerts

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python deploy_model.py <video_path> [output_path]")
        sys.exit(1)
    
    video_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    detect_theft_in_video(video_path, output_path)
'''
        
        with open("deploy_model.py", 'w') as f:
            f.write(deployment_script)
        
        print("📦 Deployment script created: deploy_model.py")

def main():
    """Main training function"""
    
    # Configuration
    data_path = "data/updated_dataset/data.yaml"
    model_size = 'n'  # Start with nano for speed
    
    # Check if updated dataset exists
    if not os.path.exists(data_path):
        print("❌ Updated dataset not found!")
        print("Please run: python update_dataset_classes.py")
        print("Then manually review and update annotations")
        return
    
    # Initialize trainer
    trainer = ThreeClassTrainer(data_path, model_size)
    
    try:
        # Train model
        results, results_dir = trainer.train_model()
        
        # Create deployment script
        best_model = results_dir / "weights" / "best.pt"
        if best_model.exists():
            trainer.create_deployment_script(str(best_model))
        
        print("\n🎉 Training pipeline completed successfully!")
        print("\nNext steps:")
        print("1. Test the model: python deploy_model.py <video_path>")
        print("2. Use advanced_theft_detector.py for detailed analysis")
        print("3. Deploy in production surveillance system")
        
    except Exception as e:
        print(f"❌ Training pipeline failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
