#!/usr/bin/env python3
"""
Cleanup and optimization script for the theft detection project
Removes unwanted files and optimizes the codebase
"""

import os
import shutil
from pathlib import Path
import json

def cleanup_old_files():
    """Remove old and unwanted files"""
    
    print("=== Cleaning up old files ===")
    
    # Files to remove
    files_to_remove = [
        "execute.py",           # Old hardcoded script
        "training.py",          # Old Colab-specific script
        "train_model.py",       # Old training script
        "simple_train.py",      # Simplified version no longer needed
        "test_model.py",        # Old testing script
        "combine_datasets.py",  # One-time use script
    ]
    
    # Directories to remove
    dirs_to_remove = [
        "combined_dataset",     # Old dataset structure
        "dataset_v1",          # Temporary extraction
        "dataset_v9",          # Temporary extraction
        "test_results",        # Old test outputs
    ]
    
    # Remove files
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"✅ Removed: {file_path}")
        else:
            print(f"⚠️  Not found: {file_path}")
    
    # Remove directories
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            print(f"✅ Removed directory: {dir_path}")
        else:
            print(f"⚠️  Directory not found: {dir_path}")
    
    # Remove temporary files
    temp_patterns = ["temp_v*", "*.tmp", "*.cache"]
    for pattern in temp_patterns:
        for file_path in Path(".").glob(pattern):
            if file_path.is_file():
                file_path.unlink()
                print(f"✅ Removed temp file: {file_path}")
            elif file_path.is_dir():
                shutil.rmtree(file_path)
                print(f"✅ Removed temp directory: {file_path}")

def organize_project_structure():
    """Organize project into clean structure"""
    
    print("\n=== Organizing project structure ===")
    
    # Create organized directory structure
    directories = {
        "src": "Source code files",
        "models": "Trained models and weights", 
        "data": "Dataset and annotations",
        "output": "Processing outputs",
        "docs": "Documentation and reports",
        "config": "Configuration files",
        "scripts": "Utility scripts"
    }
    
    for dir_name, description in directories.items():
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ Created: {dir_name}/ - {description}")
    
    # Move files to appropriate directories
    file_moves = {
        # Source code
        "advanced_theft_detector.py": "src/",
        "train_three_class_model.py": "src/",
        "web_interface.py": "src/",
        "update_dataset_classes.py": "scripts/",
        
        # Data
        "updated_dataset": "data/",
        
        # Documentation
        "research_paper.md": "docs/",
        "PROJECT_SUMMARY.md": "docs/",
        "annotation_guide.md": "docs/",
        
        # Models (if training completed)
        "runs": "models/",
        
        # Config
        "deploy_model.py": "config/",
    }
    
    for source, destination in file_moves.items():
        if os.path.exists(source):
            dest_path = Path(destination) / Path(source).name
            if Path(source).is_dir():
                if dest_path.exists():
                    shutil.rmtree(dest_path)
                shutil.move(source, destination)
            else:
                shutil.move(source, dest_path)
            print(f"✅ Moved: {source} → {destination}")

def create_requirements_file():
    """Create requirements.txt for the project"""
    
    print("\n=== Creating requirements.txt ===")
    
    requirements = [
        "ultralytics>=8.0.0",
        "opencv-python>=4.8.0",
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "numpy>=1.24.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "pandas>=2.0.0",
        "pyyaml>=6.0",
        "streamlit>=1.28.0",
        "pillow>=10.0.0",
        "pathlib",
        "json5",
        "tqdm"
    ]
    
    with open("requirements.txt", "w") as f:
        for req in requirements:
            f.write(f"{req}\n")
    
    print("✅ Created requirements.txt")

def create_main_entry_point():
    """Create main.py as the primary entry point"""
    
    print("\n=== Creating main entry point ===")
    
    main_script = '''#!/usr/bin/env python3
"""
Main entry point for Chain Snatching Theft Detection System
Choose between different modes of operation
"""

import sys
import argparse
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

def main():
    parser = argparse.ArgumentParser(
        description="Chain Snatching Theft Detection System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py web                           # Launch web interface
  python main.py detect video.mp4             # Detect theft in video
  python main.py train                         # Train new model
  python main.py --help                       # Show this help
        """
    )
    
    subparsers = parser.add_subparsers(dest='mode', help='Operation mode')
    
    # Web interface mode
    web_parser = subparsers.add_parser('web', help='Launch web interface')
    web_parser.add_argument('--port', type=int, default=8501, help='Port for web interface')
    
    # Detection mode
    detect_parser = subparsers.add_parser('detect', help='Detect theft in video')
    detect_parser.add_argument('video', help='Path to input video')
    detect_parser.add_argument('--model', default='models/runs/train/*/weights/best.pt', 
                              help='Path to model weights')
    detect_parser.add_argument('--output', help='Path to output video')
    detect_parser.add_argument('--confidence', type=float, default=0.5, 
                              help='Confidence threshold')
    
    # Training mode
    train_parser = subparsers.add_parser('train', help='Train new model')
    train_parser.add_argument('--data', default='data/updated_dataset/data.yaml',
                             help='Path to dataset YAML')
    train_parser.add_argument('--epochs', type=int, default=100, help='Training epochs')
    
    args = parser.parse_args()
    
    if args.mode == 'web':
        launch_web_interface(args.port)
    elif args.mode == 'detect':
        run_detection(args.video, args.model, args.output, args.confidence)
    elif args.mode == 'train':
        run_training(args.data, args.epochs)
    else:
        parser.print_help()

def launch_web_interface(port):
    """Launch Streamlit web interface"""
    import subprocess
    import sys
    
    print(f"🚀 Launching web interface on port {port}")
    subprocess.run([sys.executable, "-m", "streamlit", "run", "src/web_interface.py", 
                   "--server.port", str(port)])

def run_detection(video_path, model_path, output_path, confidence):
    """Run theft detection on video"""
    from advanced_theft_detector import AdvancedTheftDetector
    import glob
    
    # Find model if pattern provided
    if '*' in model_path:
        model_files = glob.glob(model_path)
        if model_files:
            model_path = model_files[0]
        else:
            print(f"❌ No model found matching: {model_path}")
            return
    
    print(f"🎬 Processing video: {video_path}")
    print(f"🤖 Using model: {model_path}")
    
    detector = AdvancedTheftDetector(model_path, confidence)
    alerts = detector.process_video(video_path, output_path)
    
    if alerts:
        print(f"🚨 {len(alerts)} security alerts generated!")
    else:
        print("✅ No threats detected")

def run_training(data_path, epochs):
    """Run model training"""
    from train_three_class_model import ThreeClassTrainer
    
    print(f"🏋️ Starting training with {epochs} epochs")
    print(f"📊 Dataset: {data_path}")
    
    trainer = ThreeClassTrainer(data_path)
    trainer.train_model()

if __name__ == "__main__":
    main()
'''
    
    with open("main.py", "w") as f:
        f.write(main_script)
    
    print("✅ Created main.py entry point")

def create_readme():
    """Create comprehensive README.md"""
    
    print("\n=== Creating README.md ===")
    
    readme_content = '''# 🚨 Chain Snatching Theft Detection System

Advanced computer vision system for real-time detection of chain snatching theft incidents using YOLOv8.

## 🎯 Features

- **Three-Class Detection**: 
  - `chain_snatch` - Theft action detection
  - `running` - Fast movement detection  
  - `suspect` - Combined theft + running (HIGH ALERT)

- **Frame-by-Frame Analysis**: Real-time video processing
- **Web Interface**: Easy-to-use Streamlit interface
- **Alert System**: Automated security alerts
- **Production Ready**: Optimized for deployment

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd theft-detection

# Install dependencies
pip install -r requirements.txt
```

### Usage

#### 1. Web Interface (Recommended)
```bash
python main.py web
```
Open http://localhost:8501 in your browser

#### 2. Command Line Detection
```bash
python main.py detect video.mp4 --output detected_video.mp4
```

#### 3. Train New Model
```bash
python main.py train --epochs 100
```

## 📁 Project Structure

```
theft-detection/
├── src/                    # Source code
│   ├── advanced_theft_detector.py
│   ├── train_three_class_model.py
│   └── web_interface.py
├── data/                   # Dataset
│   └── updated_dataset/
├── models/                 # Trained models
├── docs/                   # Documentation
├── config/                 # Configuration files
├── scripts/                # Utility scripts
├── main.py                 # Main entry point
└── requirements.txt        # Dependencies
```

## 🎯 Detection Logic

1. **Chain Snatch Detection**: Identifies theft actions
2. **Running Detection**: Monitors for fast movement
3. **Suspect Classification**: Combines theft + running for high-confidence alerts
4. **Temporal Analysis**: Tracks behavior across frames
5. **Alert Generation**: Immediate security notifications

## 📊 Model Performance

- **mAP50**: 32.07%
- **Real-time Processing**: 37 FPS
- **Model Size**: 6.2 MB
- **Classes**: 3 (chain_snatch, running, suspect)

## 🔧 Configuration

Edit `config/deploy_model.py` to customize:
- Confidence thresholds
- Alert sensitivity
- Output formats
- Processing parameters

## 📖 Documentation

- [Research Paper](docs/research_paper.md)
- [Project Summary](docs/PROJECT_SUMMARY.md)
- [Annotation Guide](docs/annotation_guide.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Ultralytics YOLOv8
- Roboflow annotation platform
- OpenCV computer vision library
'''
    
    with open("README.md", "w") as f:
        f.write(readme_content)
    
    print("✅ Created README.md")

def create_project_summary():
    """Create final project summary"""
    
    print("\n=== Creating project summary ===")
    
    summary = {
        "project_name": "Chain Snatching Theft Detection System",
        "version": "2.0.0",
        "description": "Advanced three-class theft detection with frame-by-frame analysis",
        "classes": ["chain_snatch", "running", "suspect"],
        "features": [
            "Real-time video processing",
            "Web interface",
            "Automated alerts",
            "Temporal analysis",
            "Production deployment ready"
        ],
        "performance": {
            "mAP50": "32.07%",
            "fps": 37,
            "model_size_mb": 6.2
        },
        "files": {
            "main_entry": "main.py",
            "web_interface": "src/web_interface.py", 
            "detector": "src/advanced_theft_detector.py",
            "trainer": "src/train_three_class_model.py",
            "dataset": "data/updated_dataset/",
            "models": "models/runs/train/*/weights/best.pt"
        }
    }
    
    with open("project_info.json", "w") as f:
        json.dump(summary, f, indent=2)
    
    print("✅ Created project_info.json")

def main():
    """Main cleanup and optimization function"""
    
    print("🧹 CLEANUP AND OPTIMIZATION SCRIPT")
    print("=" * 50)
    
    # Step 1: Cleanup old files
    cleanup_old_files()
    
    # Step 2: Organize structure
    organize_project_structure()
    
    # Step 3: Create requirements
    create_requirements_file()
    
    # Step 4: Create main entry point
    create_main_entry_point()
    
    # Step 5: Create documentation
    create_readme()
    
    # Step 6: Create project summary
    create_project_summary()
    
    print("\n" + "=" * 50)
    print("✅ CLEANUP AND OPTIMIZATION COMPLETE!")
    print("=" * 50)
    
    print("\n🎉 Your project is now optimized and production-ready!")
    print("\nNext steps:")
    print("1. python main.py web          # Launch web interface")
    print("2. python main.py detect video.mp4  # Test detection")
    print("3. Check README.md for full documentation")

if __name__ == "__main__":
    main()
