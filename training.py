model training:

import os
HOME = os.getcwd()
print(HOME)

!pip install ultralytics==8.0.20

from IPython import display
display.clear_output()

import ultralytics
ultralytics.checks()
from ultralytics import YOLO

from IPython.display import display, Image

%cd {HOME}
!yolo task=detect mode=predict model=yolov8n.pt conf=0.25 source='https://media.roboflow.com/notebooks/examples/dog.jpeg' save=True


from google.colab import drive
drive.mount('/content/gdrive')
!ln -s /content/gdrive/My\ Drive/ /mydrive
!ls /mydrive

!mkdir {HOME}/dataset
%cd {HOME}/dataset

!unzip /content/gdrive/MyDrive/running.zip

!pip install -U albumentations

%cd {HOME}

!yolo task=detect mode=train model=yolov8n.pt data=/content/dataset/data.yaml epochs=100 imgsz=800 plots=True