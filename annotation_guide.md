
# Annotation Guide for Three-Class Theft Detection System

## Class Definitions:

### 1. chain_snatch (Class 0)
- The actual moment of chain snatching/theft
- Includes victim-perpetrator interaction during theft
- Annotate the area where the theft is occurring
- Examples: Hand grabbing chain, pulling motion, theft action

### 2. running (Class 1) 
- Fast movement or running behavior
- Sudden acceleration or escape movement
- Annotate person(s) showing running/fast movement
- Examples: Person running away, quick escape motion

### 3. suspect (Class 2)
- COMBINATION: When both chain_snatch AND running occur
- This indicates high confidence theft scenario
- Annotate when you see theft followed by running
- This is the most critical class for alerts

## Detection Logic:
- Frame-by-frame analysis
- If chain_snatch detected -> Monitor next frames for running
- If running detected after chain_snatch -> Classify as suspect
- Generate alert when suspect class is detected

## Annotation Tips:
1. Be precise with bounding boxes
2. Focus on the action, not just the person
3. Consider temporal sequence (theft -> running)
4. Suspect class should have highest priority
