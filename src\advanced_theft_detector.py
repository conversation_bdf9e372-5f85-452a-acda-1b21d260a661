#!/usr/bin/env python3
"""
Advanced Chain Snatching Theft Detection System
Frame-by-frame video processing with three-class detection:
1. chain_snatch - Theft action
2. running - Fast movement  
3. suspect - Combination of theft + running (HIGH ALERT)
"""

import cv2
import numpy as np
import argparse
import os
from pathlib import Path
from ultralytics import YOLO
import json
from datetime import datetime
from collections import deque
import time

class AdvancedTheftDetector:
    def __init__(self, model_path, confidence_threshold=0.5):
        """
        Initialize advanced theft detection system
        
        Args:
            model_path: Path to trained YOLOv8 model
            confidence_threshold: Minimum confidence for detections
        """
        self.model = YOLO(model_path)
        self.confidence_threshold = confidence_threshold
        
        # Class definitions
        self.classes = {
            0: 'chain_snatch',
            1: 'running', 
            2: 'suspect'
        }
        
        # Detection state tracking
        self.chain_snatch_detected = False
        self.chain_snatch_frame = None
        self.running_detected = False
        self.suspect_confirmed = False
        
        # Frame buffer for temporal analysis
        self.frame_buffer = deque(maxlen=30)  # 1 second at 30fps
        self.detection_history = deque(maxlen=30)
        
        # Alert system
        self.alerts = []
        self.last_alert_time = 0
        self.alert_cooldown = 5.0  # 5 seconds between alerts
        
        print(f"Advanced Theft Detector initialized")
        print(f"Model: {model_path}")
        print(f"Classes: {list(self.classes.values())}")
    
    def detect_objects(self, frame):
        """Run detection on single frame"""
        results = self.model(frame, verbose=False)
        predictions = results[0].boxes.data.cpu().numpy() if len(results[0].boxes) > 0 else np.array([])
        return predictions
    
    def analyze_detections(self, predictions, frame_number):
        """Analyze detections and update state"""
        current_detections = {
            'chain_snatch': [],
            'running': [],
            'suspect': []
        }
        
        # Process each detection
        for pred in predictions:
            if len(pred) >= 6:
                x1, y1, x2, y2, confidence, class_id = pred[:6]
                
                if confidence > self.confidence_threshold:
                    class_name = self.classes.get(int(class_id), 'unknown')
                    
                    detection_info = {
                        'bbox': [int(x1), int(y1), int(x2), int(y2)],
                        'confidence': float(confidence),
                        'class': class_name,
                        'frame': frame_number
                    }
                    
                    current_detections[class_name].append(detection_info)
        
        # Update detection history
        self.detection_history.append(current_detections)
        
        return current_detections
    
    def temporal_analysis(self, current_detections, frame_number):
        """Perform temporal analysis to detect suspect behavior"""
        
        # Check for chain snatching
        if current_detections['chain_snatch']:
            if not self.chain_snatch_detected:
                self.chain_snatch_detected = True
                self.chain_snatch_frame = frame_number
                print(f"🚨 CHAIN SNATCH detected at frame {frame_number}")
        
        # Check for running after chain snatch
        if self.chain_snatch_detected and current_detections['running']:
            frames_since_snatch = frame_number - self.chain_snatch_frame
            
            # If running detected within 3 seconds (90 frames at 30fps) after chain snatch
            if frames_since_snatch <= 90:
                if not self.suspect_confirmed:
                    self.suspect_confirmed = True
                    self.generate_alert('SUSPECT_CONFIRMED', frame_number, current_detections)
                    print(f"🚨🚨 SUSPECT CONFIRMED at frame {frame_number} (running after theft)")
        
        # Direct suspect detection
        if current_detections['suspect']:
            if not self.suspect_confirmed:
                self.suspect_confirmed = True
                self.generate_alert('DIRECT_SUSPECT', frame_number, current_detections)
                print(f"🚨🚨 DIRECT SUSPECT detected at frame {frame_number}")
        
        # Reset state after some time if no further activity
        if self.chain_snatch_detected and frame_number - self.chain_snatch_frame > 150:  # 5 seconds
            if not self.suspect_confirmed:
                print(f"Chain snatch timeout - resetting state at frame {frame_number}")
                self.reset_detection_state()
    
    def generate_alert(self, alert_type, frame_number, detections):
        """Generate security alert"""
        current_time = time.time()
        
        # Prevent spam alerts
        if current_time - self.last_alert_time < self.alert_cooldown:
            return
        
        alert = {
            'type': alert_type,
            'frame': frame_number,
            'timestamp': datetime.now().isoformat(),
            'detections': detections,
            'severity': 'HIGH' if 'SUSPECT' in alert_type else 'MEDIUM'
        }
        
        self.alerts.append(alert)
        self.last_alert_time = current_time
        
        print(f"🚨 ALERT GENERATED: {alert_type} at frame {frame_number}")
    
    def reset_detection_state(self):
        """Reset detection state"""
        self.chain_snatch_detected = False
        self.chain_snatch_frame = None
        self.running_detected = False
        self.suspect_confirmed = False
    
    def draw_detections(self, frame, detections, frame_number):
        """Draw detections and status on frame"""
        
        # Color scheme
        colors = {
            'chain_snatch': (0, 0, 255),    # Red
            'running': (255, 0, 0),         # Blue  
            'suspect': (0, 255, 255)        # Yellow (HIGH ALERT)
        }
        
        # Draw detections
        for class_name, detection_list in detections.items():
            color = colors.get(class_name, (255, 255, 255))
            
            for detection in detection_list:
                bbox = detection['bbox']
                confidence = detection['confidence']
                
                # Draw bounding box
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 3)
                
                # Draw label
                label = f"{class_name}: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                
                # Background for text
                cv2.rectangle(frame, 
                            (bbox[0], bbox[1] - label_size[1] - 10),
                            (bbox[0] + label_size[0], bbox[1]),
                            color, -1)
                
                # Text
                cv2.putText(frame, label, 
                          (bbox[0], bbox[1] - 5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Draw status information
        self.draw_status(frame, frame_number)
        
        return frame
    
    def draw_status(self, frame, frame_number):
        """Draw system status on frame"""
        h, w = frame.shape[:2]
        
        # Status panel background
        cv2.rectangle(frame, (10, 10), (400, 120), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (400, 120), (255, 255, 255), 2)
        
        # Status text
        status_lines = [
            f"Frame: {frame_number}",
            f"Chain Snatch: {'YES' if self.chain_snatch_detected else 'NO'}",
            f"Suspect: {'CONFIRMED' if self.suspect_confirmed else 'MONITORING'}",
            f"Alerts: {len(self.alerts)}"
        ]
        
        for i, line in enumerate(status_lines):
            color = (0, 255, 0) if 'NO' in line or 'MONITORING' in line else (0, 0, 255)
            cv2.putText(frame, line, (20, 35 + i * 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # Alert indicator
        if self.suspect_confirmed:
            cv2.rectangle(frame, (w-150, 10), (w-10, 60), (0, 255, 255), -1)
            cv2.putText(frame, "HIGH ALERT", (w-140, 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    def process_video(self, video_path, output_path=None, save_alerts=True):
        """Process video with frame-by-frame analysis"""
        print(f"Processing video: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {video_path}")
        
        # Video properties
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Video: {frame_width}x{frame_height}, {fps} FPS, {total_frames} frames")
        
        # Output video writer
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (frame_width, frame_height))
        
        frame_number = 0
        
        try:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Store frame in buffer
                self.frame_buffer.append(frame.copy())
                
                # Detect objects
                predictions = self.detect_objects(frame)
                
                # Analyze detections
                current_detections = self.analyze_detections(predictions, frame_number)
                
                # Temporal analysis for suspect detection
                self.temporal_analysis(current_detections, frame_number)
                
                # Draw results
                processed_frame = self.draw_detections(frame, current_detections, frame_number)
                
                # Write to output
                if out:
                    out.write(processed_frame)
                
                # Progress update
                if frame_number % 30 == 0:
                    progress = (frame_number / total_frames) * 100
                    print(f"Progress: {progress:.1f}% - Frame {frame_number}/{total_frames}")
                
                frame_number += 1
        
        finally:
            cap.release()
            if out:
                out.release()
            cv2.destroyAllWindows()
        
        # Save alerts
        if save_alerts and self.alerts:
            alert_path = Path(output_path).parent / f"{Path(video_path).stem}_alerts.json" if output_path else f"{Path(video_path).stem}_alerts.json"
            with open(alert_path, 'w') as f:
                json.dump(self.alerts, f, indent=2)
            print(f"Alerts saved to: {alert_path}")
        
        # Print summary
        self.print_summary(frame_number)
        
        return self.alerts
    
    def print_summary(self, total_frames):
        """Print processing summary"""
        print(f"\n{'='*50}")
        print(f"PROCESSING SUMMARY")
        print(f"{'='*50}")
        print(f"Total frames processed: {total_frames}")
        print(f"Chain snatch detected: {'YES' if self.chain_snatch_detected else 'NO'}")
        print(f"Suspect confirmed: {'YES' if self.suspect_confirmed else 'NO'}")
        print(f"Total alerts generated: {len(self.alerts)}")
        
        if self.alerts:
            print(f"\nALERT DETAILS:")
            for i, alert in enumerate(self.alerts, 1):
                print(f"  {i}. {alert['type']} at frame {alert['frame']} - {alert['severity']}")

def main():
    parser = argparse.ArgumentParser(description='Advanced Chain Snatching Theft Detection')
    parser.add_argument('--video', required=True, help='Path to input video')
    parser.add_argument('--model', required=True, help='Path to trained model')
    parser.add_argument('--output', help='Path to output video')
    parser.add_argument('--confidence', type=float, default=0.5, help='Confidence threshold')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.video):
        print(f"Error: Video file not found: {args.video}")
        return
    
    if not os.path.exists(args.model):
        print(f"Error: Model file not found: {args.model}")
        return
    
    # Create detector
    detector = AdvancedTheftDetector(
        model_path=args.model,
        confidence_threshold=args.confidence
    )
    
    # Process video
    try:
        alerts = detector.process_video(args.video, args.output)
        print("✅ Processing completed successfully!")
        
        if alerts:
            print(f"🚨 {len(alerts)} security alerts generated!")
        else:
            print("✅ No security threats detected.")
            
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")

if __name__ == "__main__":
    main()
