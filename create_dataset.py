#!/usr/bin/env python3
"""
Create a proper three-class dataset from all available data
"""

import os
import shutil
import yaml
from pathlib import Path
import hashlib

def get_file_hash(filepath):
    """Get MD5 hash of a file to detect duplicates"""
    hash_md5 = hashlib.md5()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def create_three_class_dataset():
    """Create three-class dataset from all available sources"""
    
    print("=== Creating Three-Class Dataset ===")
    
    # Create output directory
    output_dir = Path("final_dataset")
    output_dir.mkdir(exist_ok=True)
    
    for split in ['train', 'valid', 'test']:
        (output_dir / split / 'images').mkdir(parents=True, exist_ok=True)
        (output_dir / split / 'labels').mkdir(parents=True, exist_ok=True)
    
    # Track unique files
    seen_hashes = set()
    file_counter = 0
    
    # Source directories to process
    source_dirs = [
        "TheftDetection_Combined",
        "temp1", "temp2", "temp3", "temp4", "temp5", 
        "temp6", "temp7", "temp8"
    ]
    
    for source_dir in source_dirs:
        if not os.path.exists(source_dir):
            continue
            
        print(f"Processing: {source_dir}")
        
        # Process each split
        for split in ['train', 'valid', 'test']:
            source_split = Path(source_dir) / split
            if not source_split.exists():
                continue
                
            images_dir = source_split / 'images'
            labels_dir = source_split / 'labels'
            
            if not images_dir.exists():
                continue
            
            # Process images
            for img_file in images_dir.glob('*.jpg'):
                # Check for duplicates
                file_hash = get_file_hash(img_file)
                if file_hash in seen_hashes:
                    continue
                
                seen_hashes.add(file_hash)
                
                # Copy image
                new_name = f"image_{file_counter:06d}.jpg"
                dest_img = output_dir / split / 'images' / new_name
                shutil.copy2(img_file, dest_img)
                
                # Copy and update label if exists
                label_file = labels_dir / (img_file.stem + '.txt')
                if label_file.exists():
                    new_label_name = f"image_{file_counter:06d}.txt"
                    dest_label = output_dir / split / 'labels' / new_label_name
                    
                    # Update label classes to three-class system
                    update_label_classes(label_file, dest_label)
                
                file_counter += 1
                
                if file_counter % 10 == 0:
                    print(f"  Processed {file_counter} images...")
    
    # Create data.yaml
    data_yaml = {
        'train': 'train/images',
        'val': 'valid/images',
        'test': 'test/images',
        'nc': 3,
        'names': ['chain_snatch', 'running', 'suspect']
    }
    
    with open(output_dir / 'data.yaml', 'w') as f:
        yaml.dump(data_yaml, f, default_flow_style=False)
    
    # Print statistics
    print(f"\n=== Dataset Creation Complete ===")
    print(f"Total unique images: {file_counter}")
    
    for split in ['train', 'valid', 'test']:
        img_count = len(list((output_dir / split / 'images').glob('*.jpg')))
        label_count = len(list((output_dir / split / 'labels').glob('*.txt')))
        print(f"{split}: {img_count} images, {label_count} labels")
    
    return output_dir

def update_label_classes(source_label, dest_label):
    """Update label file to three-class system"""
    
    # Class mapping from various formats to three-class
    # Most datasets have: 0=Suspect-Victim, 1=suspect, etc.
    # Map to: 0=chain_snatch, 1=running, 2=suspect
    
    class_mapping = {
        0: 0,  # Suspect-Victim -> chain_snatch
        1: 2,  # suspect -> suspect
        2: 2,  # suspect 2 -> suspect
        3: 0,  # victim -> chain_snatch
    }
    
    updated_lines = []
    
    try:
        with open(source_label, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        old_class = int(parts[0])
                        new_class = class_mapping.get(old_class, 0)  # Default to chain_snatch
                        
                        # Update class ID
                        parts[0] = str(new_class)
                        updated_lines.append(' '.join(parts))
    except Exception as e:
        print(f"Warning: Could not process label {source_label}: {e}")
        return
    
    # Write updated labels
    try:
        with open(dest_label, 'w') as f:
            for line in updated_lines:
                f.write(line + '\n')
    except Exception as e:
        print(f"Warning: Could not write label {dest_label}: {e}")

def main():
    """Main function"""
    
    # Create the dataset
    dataset_dir = create_three_class_dataset()
    
    print(f"\n✅ Three-class dataset created at: {dataset_dir}")
    print("\nNext steps:")
    print("1. python src/train_three_class_model.py")
    print("2. Wait for training to complete")
    print("3. Test with: python src/advanced_theft_detector.py")

if __name__ == "__main__":
    main()
