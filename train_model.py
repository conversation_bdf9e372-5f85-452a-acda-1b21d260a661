#!/usr/bin/env python3
"""
Improved training script for Chain Snatching Theft Detection using YOLOv8
"""

import os
import sys
from pathlib import Path
import yaml
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def setup_environment():
    """Setup training environment and check requirements"""
    print("=== Setting up training environment ===")
    
    # Check if CUDA is available
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    if device == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"CUDA Version: {torch.version.cuda}")
    
    return device

def validate_dataset(data_path):
    """Validate dataset structure and configuration"""
    print("=== Validating dataset ===")
    
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"Dataset configuration not found: {data_path}")
    
    with open(data_path, 'r') as f:
        data = yaml.safe_load(f)
    
    # Check required fields
    required_fields = ['train', 'val', 'nc', 'names']
    for field in required_fields:
        if field not in data:
            raise ValueError(f"Missing required field in data.yaml: {field}")
    
    # Validate paths
    base_dir = Path(data_path).parent
    for split in ['train', 'val', 'test']:
        if split in data:
            split_path = base_dir / data[split]
            if not split_path.exists():
                raise FileNotFoundError(f"Dataset split path not found: {split_path}")
            
            # Count images
            img_count = len(list(split_path.glob('*.jpg'))) + len(list(split_path.glob('*.png')))
            print(f"{split}: {img_count} images")
    
    print(f"Classes ({data['nc']}): {data['names']}")
    return data

def train_model(data_path, model_size='n', epochs=100, imgsz=640, batch_size=16):
    """Train YOLOv8 model with specified parameters"""
    print("=== Starting model training ===")
    
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(f"runs/train/theft_detection_{timestamp}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize model
    model_name = f"yolov8{model_size}.pt"
    print(f"Loading model: {model_name}")
    model = YOLO(model_name)
    
    # Training parameters
    train_params = {
        'data': data_path,
        'epochs': epochs,
        'imgsz': imgsz,
        'batch': batch_size,
        'device': setup_environment(),
        'project': 'runs/train',
        'name': f'theft_detection_{timestamp}',
        'save': True,
        'save_period': 10,  # Save checkpoint every 10 epochs
        'plots': True,
        'val': True,
        'patience': 50,  # Early stopping patience
        'optimizer': 'AdamW',
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        'pose': 12.0,
        'kobj': 1.0,
        'label_smoothing': 0.0,
        'nbs': 64,
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0
    }
    
    print(f"Training parameters:")
    for key, value in train_params.items():
        print(f"  {key}: {value}")
    
    # Start training
    try:
        results = model.train(**train_params)
        print("=== Training completed successfully ===")
        
        # Print final metrics
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            print(f"Final mAP50: {metrics.get('metrics/mAP50(B)', 'N/A')}")
            print(f"Final mAP50-95: {metrics.get('metrics/mAP50-95(B)', 'N/A')}")
        
        return results, output_dir
        
    except Exception as e:
        print(f"Training failed: {str(e)}")
        raise

def evaluate_model(model_path, data_path):
    """Evaluate trained model and generate metrics"""
    print("=== Evaluating model ===")
    
    model = YOLO(model_path)
    
    # Run validation
    results = model.val(data=data_path, save_json=True, save_hybrid=True)
    
    print(f"Validation mAP50: {results.box.map50}")
    print(f"Validation mAP50-95: {results.box.map}")
    
    return results

def main():
    """Main training pipeline"""
    print("=== Chain Snatching Theft Detection - Model Training ===")
    
    # Configuration
    data_path = "combined_dataset/data.yaml"
    model_size = 'n'  # nano, small, medium, large, xlarge
    epochs = 100
    imgsz = 640
    batch_size = 16
    
    try:
        # Validate dataset
        dataset_info = validate_dataset(data_path)
        
        # Train model
        results, output_dir = train_model(
            data_path=data_path,
            model_size=model_size,
            epochs=epochs,
            imgsz=imgsz,
            batch_size=batch_size
        )
        
        # Find best model
        best_model_path = output_dir / "weights" / "best.pt"
        if best_model_path.exists():
            print(f"Best model saved at: {best_model_path}")
            
            # Evaluate model
            eval_results = evaluate_model(str(best_model_path), data_path)
            
            # Save training summary
            summary = {
                'dataset': dataset_info,
                'training_params': {
                    'model_size': model_size,
                    'epochs': epochs,
                    'imgsz': imgsz,
                    'batch_size': batch_size
                },
                'results': {
                    'mAP50': float(eval_results.box.map50),
                    'mAP50-95': float(eval_results.box.map),
                    'best_model_path': str(best_model_path)
                }
            }
            
            with open(output_dir / "training_summary.yaml", 'w') as f:
                yaml.dump(summary, f, default_flow_style=False)
            
            print(f"Training summary saved to: {output_dir / 'training_summary.yaml'}")
        
    except Exception as e:
        print(f"Training pipeline failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
