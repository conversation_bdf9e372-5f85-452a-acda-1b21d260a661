#!/usr/bin/env python3
"""
Test script for the upgraded three-class theft detection system
"""

import sys
import os
from pathlib import Path
import cv2
import numpy as np

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_model_loading():
    """Test if the trained model can be loaded"""
    print("=== Testing Model Loading ===")
    
    try:
        from ultralytics import YOLO
        
        # Find the best model
        model_paths = list(Path("models/runs/train").glob("*/weights/best.pt"))
        if not model_paths:
            print("❌ No trained model found!")
            print("Please complete training first: python src/train_three_class_model.py")
            return False
        
        model_path = model_paths[-1]  # Get latest model
        print(f"Loading model: {model_path}")
        
        model = YOLO(str(model_path))
        print(f"✅ Model loaded successfully!")
        print(f"Classes: {model.names}")
        
        return True, model_path
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False, None

def test_detection_system():
    """Test the advanced detection system"""
    print("\n=== Testing Detection System ===")
    
    try:
        from advanced_theft_detector import AdvancedTheftDetector
        
        # Find model
        success, model_path = test_model_loading()
        if not success:
            return False
        
        # Initialize detector
        detector = AdvancedTheftDetector(str(model_path), confidence_threshold=0.5)
        print("✅ Detection system initialized!")
        
        # Test with a sample frame
        test_frame = np.zeros((640, 640, 3), dtype=np.uint8)
        predictions = detector.detect_objects(test_frame)
        print(f"✅ Detection test passed! Found {len(predictions)} objects")
        
        return True
        
    except Exception as e:
        print(f"❌ Detection system test failed: {e}")
        return False

def test_dataset():
    """Test dataset structure"""
    print("\n=== Testing Dataset ===")
    
    dataset_path = Path("data/updated_dataset")
    if not dataset_path.exists():
        print("❌ Dataset not found!")
        return False
    
    # Check data.yaml
    yaml_path = dataset_path / "data.yaml"
    if not yaml_path.exists():
        print("❌ data.yaml not found!")
        return False
    
    # Count images
    total_images = 0
    for split in ['train', 'valid', 'test']:
        split_path = dataset_path / split / 'images'
        if split_path.exists():
            img_count = len(list(split_path.glob('*.jpg')))
            total_images += img_count
            print(f"✅ {split}: {img_count} images")
        else:
            print(f"❌ {split} directory not found!")
    
    print(f"✅ Total dataset: {total_images} images")
    return total_images > 0

def test_video_processing():
    """Test video processing capability"""
    print("\n=== Testing Video Processing ===")
    
    # Create a test video
    test_video_path = "test_video.mp4"
    
    try:
        # Create a simple test video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(test_video_path, fourcc, 30.0, (640, 480))
        
        # Write 30 frames (1 second)
        for i in range(30):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            # Add some text
            cv2.putText(frame, f"Frame {i}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            out.write(frame)
        
        out.release()
        print(f"✅ Test video created: {test_video_path}")
        
        # Test if we can read it
        cap = cv2.VideoCapture(test_video_path)
        if cap.isOpened():
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            print(f"✅ Video readable: {frame_count} frames")
            cap.release()
            
            # Clean up
            os.remove(test_video_path)
            return True
        else:
            print("❌ Cannot read test video!")
            return False
            
    except Exception as e:
        print(f"❌ Video processing test failed: {e}")
        return False

def test_project_structure():
    """Test project structure"""
    print("\n=== Testing Project Structure ===")
    
    required_dirs = ["src", "data", "models", "docs"]
    required_files = ["main.py", "requirements.txt"]
    
    all_good = True
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✅ Directory exists: {dir_name}/")
        else:
            print(f"❌ Missing directory: {dir_name}/")
            all_good = False
    
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✅ File exists: {file_name}")
        else:
            print(f"❌ Missing file: {file_name}")
            all_good = False
    
    return all_good

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 COMPREHENSIVE SYSTEM TEST")
    print("=" * 40)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Dataset", test_dataset),
        ("Video Processing", test_video_processing),
        ("Model Loading", lambda: test_model_loading()[0]),
        ("Detection System", test_detection_system),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for use.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

def main():
    """Main test function"""
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "model":
            test_model_loading()
        elif test_type == "dataset":
            test_dataset()
        elif test_type == "video":
            test_video_processing()
        elif test_type == "structure":
            test_project_structure()
        elif test_type == "detection":
            test_detection_system()
        else:
            print("Available tests: model, dataset, video, structure, detection")
    else:
        # Run comprehensive test
        run_comprehensive_test()

if __name__ == "__main__":
    main()
