#!/usr/bin/env python3
"""
Test the trained theft detection model on sample images
"""

import cv2
import os
from ultralytics import YOLO
from pathlib import Path
import matplotlib.pyplot as plt

def test_model_on_images():
    """Test the trained model on sample images from the dataset"""
    
    # Load the trained model
    model_path = "runs/detect/train/weights/best.pt"
    if not os.path.exists(model_path):
        print(f"Error: Trained model not found at {model_path}")
        return
    
    print(f"Loading model: {model_path}")
    model = YOLO(model_path)
    
    # Test on some images from the test set
    test_images_dir = Path("combined_dataset/test/images")
    if not test_images_dir.exists():
        print(f"Error: Test images directory not found: {test_images_dir}")
        return
    
    # Get test images
    test_images = list(test_images_dir.glob("*.jpg"))
    if not test_images:
        print("No test images found!")
        return
    
    print(f"Found {len(test_images)} test images")
    
    # Create output directory
    output_dir = Path("test_results")
    output_dir.mkdir(exist_ok=True)
    
    # Test each image
    for i, img_path in enumerate(test_images[:5]):  # Test first 5 images
        print(f"Testing image {i+1}: {img_path.name}")
        
        # Run inference
        results = model(str(img_path))
        
        # Save results
        output_path = output_dir / f"result_{i+1}_{img_path.name}"
        
        # Plot and save
        result_img = results[0].plot()
        cv2.imwrite(str(output_path), result_img)
        
        # Print detections
        if len(results[0].boxes) > 0:
            boxes = results[0].boxes
            print(f"  Detections: {len(boxes)} objects")
            for j, box in enumerate(boxes):
                conf = box.conf.item()
                cls = int(box.cls.item())
                class_name = model.names[cls]
                print(f"    {j+1}: {class_name} (confidence: {conf:.3f})")
        else:
            print("  No detections")
        
        print(f"  Result saved to: {output_path}")
    
    print(f"\nAll test results saved to: {output_dir}")

def evaluate_model_metrics():
    """Evaluate model and print detailed metrics"""
    
    model_path = "runs/detect/train/weights/best.pt"
    model = YOLO(model_path)
    
    print("=== Model Evaluation ===")
    
    # Run validation on the dataset
    results = model.val(data="combined_dataset/data.yaml")
    
    print(f"Overall mAP50: {results.box.map50:.4f}")
    print(f"Overall mAP50-95: {results.box.map:.4f}")
    
    # Per-class metrics
    if hasattr(results.box, 'ap_class_index') and results.box.ap_class_index is not None:
        print("\nPer-class metrics:")
        for i, class_idx in enumerate(results.box.ap_class_index):
            class_name = model.names[int(class_idx)]
            ap50 = results.box.ap50[i] if i < len(results.box.ap50) else 0
            ap = results.box.ap[i] if i < len(results.box.ap) else 0
            print(f"  {class_name}: mAP50={ap50:.4f}, mAP50-95={ap:.4f}")

def main():
    print("=== Testing Trained Theft Detection Model ===")
    
    # Test on sample images
    test_model_on_images()
    
    print("\n" + "="*50)
    
    # Evaluate metrics
    evaluate_model_metrics()

if __name__ == "__main__":
    main()
