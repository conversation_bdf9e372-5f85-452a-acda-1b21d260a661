#!/usr/bin/env python3
"""
Script to combine all Roboflow dataset versions into a single consolidated dataset
"""

import os
import shutil
import zipfile
import yaml
from pathlib import Path
import hashlib

def get_file_hash(filepath):
    """Get MD5 hash of a file to detect duplicates"""
    hash_md5 = hashlib.md5()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def extract_dataset(zip_path, extract_dir):
    """Extract a dataset ZIP file"""
    print(f"Extracting {zip_path}...")
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_dir)

def combine_datasets():
    """Combine all dataset versions into one"""
    
    # Create output directory structure
    output_dir = Path("combined_dataset")
    output_dir.mkdir(exist_ok=True)
    
    for split in ['train', 'valid', 'test']:
        (output_dir / split / 'images').mkdir(parents=True, exist_ok=True)
        (output_dir / split / 'labels').mkdir(parents=True, exist_ok=True)
    
    # Track unique files to avoid duplicates
    seen_hashes = set()
    file_counter = 0
    
    # Dataset versions to process (skip v6 as it doesn't exist)
    versions = [1, 2, 3, 4, 5, 7, 8, 9]
    
    # Collect all class names across versions
    all_classes = set()
    
    for version in versions:
        zip_path = f"Theft Detection.v{version}i.yolov8.zip"
        if not os.path.exists(zip_path):
            print(f"Warning: {zip_path} not found, skipping...")
            continue
            
        # Extract to temporary directory
        temp_dir = f"temp_v{version}"
        extract_dataset(zip_path, temp_dir)
        
        # Read data.yaml to get class names
        yaml_path = os.path.join(temp_dir, "data.yaml")
        if os.path.exists(yaml_path):
            with open(yaml_path, 'r') as f:
                data = yaml.safe_load(f)
                if 'names' in data:
                    all_classes.update(data['names'])
        
        # Process each split
        for split in ['train', 'valid', 'test']:
            split_dir = Path(temp_dir) / split
            if not split_dir.exists():
                continue
                
            images_dir = split_dir / 'images'
            labels_dir = split_dir / 'labels'
            
            if images_dir.exists():
                for img_file in images_dir.glob('*.jpg'):
                    # Check for duplicates
                    file_hash = get_file_hash(img_file)
                    if file_hash in seen_hashes:
                        print(f"Duplicate found, skipping: {img_file.name}")
                        continue
                    
                    seen_hashes.add(file_hash)
                    
                    # Copy image with new name
                    new_name = f"image_{file_counter:06d}.jpg"
                    shutil.copy2(img_file, output_dir / split / 'images' / new_name)
                    
                    # Copy corresponding label if exists
                    label_file = labels_dir / (img_file.stem + '.txt')
                    if label_file.exists():
                        new_label_name = f"image_{file_counter:06d}.txt"
                        shutil.copy2(label_file, output_dir / split / 'labels' / new_label_name)
                    
                    file_counter += 1
        
        # Clean up temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    # Create unified data.yaml
    class_list = sorted(list(all_classes))
    data_yaml = {
        'train': 'train/images',
        'val': 'valid/images', 
        'test': 'test/images',
        'nc': len(class_list),
        'names': class_list
    }
    
    with open(output_dir / 'data.yaml', 'w') as f:
        yaml.dump(data_yaml, f, default_flow_style=False)
    
    # Print statistics
    print("\n=== Dataset Combination Complete ===")
    print(f"Total unique images: {file_counter}")
    print(f"Classes found: {class_list}")
    
    for split in ['train', 'valid', 'test']:
        img_count = len(list((output_dir / split / 'images').glob('*.jpg')))
        label_count = len(list((output_dir / split / 'labels').glob('*.txt')))
        print(f"{split}: {img_count} images, {label_count} labels")
    
    return output_dir

if __name__ == "__main__":
    combine_datasets()
