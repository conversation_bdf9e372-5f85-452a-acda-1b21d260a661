#!/usr/bin/env python3
"""
Improved inference script for Chain Snatching Theft Detection
Supports both single model and dual model (theft + running) detection
"""

import cv2
import numpy as np
import argparse
import os
from pathlib import Path
from ultralytics import YOLO
import yaml
from datetime import datetime
import json

class TheftDetector:
    def __init__(self, theft_model_path, running_model_path=None, confidence_threshold=0.6):
        """
        Initialize theft detection system
        
        Args:
            theft_model_path: Path to theft detection model
            running_model_path: Optional path to running detection model
            confidence_threshold: Minimum confidence for detections
        """
        self.theft_model = YOLO(theft_model_path)
        self.running_model = YOLO(running_model_path) if running_model_path else None
        self.confidence_threshold = confidence_threshold
        self.theft_detected = False
        self.detection_log = []
        
        print(f"Loaded theft model: {theft_model_path}")
        if self.running_model:
            print(f"Loaded running model: {running_model_path}")
    
    def detect_theft(self, frame):
        """Detect theft in frame"""
        results = self.theft_model(frame, verbose=False)
        predictions = results[0].boxes.data.cpu().numpy() if len(results[0].boxes) > 0 else np.array([])
        return predictions
    
    def detect_running(self, frame):
        """Detect sudden running in frame"""
        if self.running_model is None:
            return np.array([])
        
        results = self.running_model(frame, verbose=False)
        predictions = results[0].boxes.data.cpu().numpy() if len(results[0].boxes) > 0 else np.array([])
        return predictions
    
    def draw_predictions(self, frame, predictions, label, color, confidence_threshold=None):
        """Draw bounding boxes and labels on frame"""
        if confidence_threshold is None:
            confidence_threshold = self.confidence_threshold
            
        for pred in predictions:
            if len(pred) >= 6:
                x1, y1, x2, y2, confidence, class_id = pred[:6]
                if confidence > confidence_threshold:
                    # Draw bounding box
                    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                    
                    # Draw label with confidence
                    label_text = f"{label}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    
                    # Draw background for text
                    cv2.rectangle(frame, 
                                (int(x1), int(y1) - label_size[1] - 10),
                                (int(x1) + label_size[0], int(y1)),
                                color, -1)
                    
                    # Draw text
                    cv2.putText(frame, label_text, 
                              (int(x1), int(y1) - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return frame
    
    def process_frame(self, frame, frame_number):
        """Process single frame for theft detection"""
        # Detect theft
        theft_predictions = self.detect_theft(frame)
        frame_with_detections = self.draw_predictions(frame, theft_predictions, "Theft", (0, 0, 255))
        
        # Check if theft is detected
        theft_detected_in_frame = any(pred[4] > self.confidence_threshold for pred in theft_predictions)
        
        if theft_detected_in_frame:
            self.theft_detected = True
            self.detection_log.append({
                'frame': frame_number,
                'type': 'theft',
                'confidence': float(max(theft_predictions[:, 4])) if len(theft_predictions) > 0 else 0.0,
                'timestamp': datetime.now().isoformat()
            })
        
        # If theft detected, also check for running
        if self.theft_detected and self.running_model:
            running_predictions = self.detect_running(frame)
            frame_with_detections = self.draw_predictions(
                frame_with_detections, running_predictions, "Running", (255, 0, 0), 0.3
            )
            
            running_detected = any(pred[4] > 0.3 for pred in running_predictions)
            if running_detected:
                self.detection_log.append({
                    'frame': frame_number,
                    'type': 'running_after_theft',
                    'confidence': float(max(running_predictions[:, 4])) if len(running_predictions) > 0 else 0.0,
                    'timestamp': datetime.now().isoformat()
                })
                return frame_with_detections, True  # Signal to stop processing
        
        return frame_with_detections, False
    
    def process_video(self, video_path, output_path=None, save_detections=True):
        """Process entire video for theft detection"""
        print(f"Processing video: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {video_path}")
        
        # Get video properties
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Video properties: {frame_width}x{frame_height}, {fps} FPS, {total_frames} frames")
        
        # Setup output video writer if output path provided
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (frame_width, frame_height))
            print(f"Output will be saved to: {output_path}")
        
        frame_number = 0
        
        try:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process frame
                processed_frame, should_stop = self.process_frame(frame, frame_number)
                
                # Write frame to output video
                if out:
                    out.write(processed_frame)
                
                # Display progress
                if frame_number % 30 == 0:  # Every 30 frames
                    progress = (frame_number / total_frames) * 100
                    print(f"Progress: {progress:.1f}% (Frame {frame_number}/{total_frames})")
                
                frame_number += 1
                
                # Stop if running after theft detected
                if should_stop:
                    print("Running after theft detected - stopping processing")
                    break
        
        finally:
            cap.release()
            if out:
                out.release()
            cv2.destroyAllWindows()
        
        # Save detection log
        if save_detections and self.detection_log:
            log_path = Path(output_path).parent / f"{Path(video_path).stem}_detections.json" if output_path else f"{Path(video_path).stem}_detections.json"
            with open(log_path, 'w') as f:
                json.dump(self.detection_log, f, indent=2)
            print(f"Detection log saved to: {log_path}")
        
        # Print summary
        print(f"\n=== Detection Summary ===")
        print(f"Total frames processed: {frame_number}")
        print(f"Theft detected: {'Yes' if self.theft_detected else 'No'}")
        print(f"Total detections: {len(self.detection_log)}")
        
        return self.detection_log

def main():
    parser = argparse.ArgumentParser(description='Chain Snatching Theft Detection Inference')
    parser.add_argument('--video', required=True, help='Path to input video')
    parser.add_argument('--theft_model', required=True, help='Path to theft detection model')
    parser.add_argument('--running_model', help='Path to running detection model (optional)')
    parser.add_argument('--output', help='Path to output video (optional)')
    parser.add_argument('--confidence', type=float, default=0.6, help='Confidence threshold')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.video):
        print(f"Error: Video file not found: {args.video}")
        return
    
    if not os.path.exists(args.theft_model):
        print(f"Error: Theft model not found: {args.theft_model}")
        return
    
    if args.running_model and not os.path.exists(args.running_model):
        print(f"Error: Running model not found: {args.running_model}")
        return
    
    # Create detector
    detector = TheftDetector(
        theft_model_path=args.theft_model,
        running_model_path=args.running_model,
        confidence_threshold=args.confidence
    )
    
    # Process video
    try:
        detections = detector.process_video(args.video, args.output)
        print("Processing completed successfully!")
        
    except Exception as e:
        print(f"Error during processing: {str(e)}")

if __name__ == "__main__":
    main()
