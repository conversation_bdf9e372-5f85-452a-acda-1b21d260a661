#!/usr/bin/env python3
"""
Update dataset to new three-class system: chain_snatch, running, suspect
Based on reference research paper approach
"""

import os
import yaml
import shutil
from pathlib import Path

def update_dataset_structure():
    """Update the dataset to new three-class system"""
    
    print("=== Updating Dataset to Three-Class System ===")
    print("New classes: chain_snatch, running, suspect")
    
    # Create new dataset directory
    new_dataset_dir = Path("updated_dataset")
    new_dataset_dir.mkdir(exist_ok=True)
    
    for split in ['train', 'valid', 'test']:
        (new_dataset_dir / split / 'images').mkdir(parents=True, exist_ok=True)
        (new_dataset_dir / split / 'labels').mkdir(parents=True, exist_ok=True)
    
    # Copy images from old dataset
    old_dataset_dir = Path("combined_dataset")
    
    for split in ['train', 'valid', 'test']:
        old_images = old_dataset_dir / split / 'images'
        old_labels = old_dataset_dir / split / 'labels'
        
        new_images = new_dataset_dir / split / 'images'
        new_labels = new_dataset_dir / split / 'labels'
        
        if old_images.exists():
            # Copy all images
            for img_file in old_images.glob('*.jpg'):
                shutil.copy2(img_file, new_images / img_file.name)
            
            # Update label files with new class mapping
            if old_labels.exists():
                for label_file in old_labels.glob('*.txt'):
                    update_label_file(label_file, new_labels / label_file.name)
    
    # Create new data.yaml
    new_data_yaml = {
        'train': 'train/images',
        'val': 'valid/images',
        'test': 'test/images',
        'nc': 3,
        'names': ['chain_snatch', 'running', 'suspect']
    }
    
    with open(new_dataset_dir / 'data.yaml', 'w') as f:
        yaml.dump(new_data_yaml, f, default_flow_style=False)
    
    print(f"Updated dataset created at: {new_dataset_dir}")
    return new_dataset_dir

def update_label_file(old_label_path, new_label_path):
    """Update individual label file with new class mapping"""
    
    # Old to new class mapping
    # Based on the logic: suspect = chain_snatch + running
    class_mapping = {
        0: 0,  # 'Suspect-Victim' -> 'chain_snatch'
        1: 2,  # 'suspect' -> 'suspect' (when both occur)
        2: 2,  # 'suspect 2' -> 'suspect' 
        3: 0   # 'victim' -> 'chain_snatch' (part of theft)
    }
    
    if not old_label_path.exists():
        return
    
    updated_lines = []
    
    with open(old_label_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 5:
                    old_class = int(parts[0])
                    new_class = class_mapping.get(old_class, 0)
                    
                    # Update class ID
                    parts[0] = str(new_class)
                    updated_lines.append(' '.join(parts))
    
    # Write updated labels
    with open(new_label_path, 'w') as f:
        for line in updated_lines:
            f.write(line + '\n')

def create_sample_annotations():
    """Create sample annotations for the new classes"""
    
    print("\n=== Creating Sample Annotations Guide ===")
    
    annotation_guide = """
# Annotation Guide for Three-Class Theft Detection System

## Class Definitions:

### 1. chain_snatch (Class 0)
- The actual moment of chain snatching/theft
- Includes victim-perpetrator interaction during theft
- Annotate the area where the theft is occurring
- Examples: Hand grabbing chain, pulling motion, theft action

### 2. running (Class 1) 
- Fast movement or running behavior
- Sudden acceleration or escape movement
- Annotate person(s) showing running/fast movement
- Examples: Person running away, quick escape motion

### 3. suspect (Class 2)
- COMBINATION: When both chain_snatch AND running occur
- This indicates high confidence theft scenario
- Annotate when you see theft followed by running
- This is the most critical class for alerts

## Detection Logic:
- Frame-by-frame analysis
- If chain_snatch detected -> Monitor next frames for running
- If running detected after chain_snatch -> Classify as suspect
- Generate alert when suspect class is detected

## Annotation Tips:
1. Be precise with bounding boxes
2. Focus on the action, not just the person
3. Consider temporal sequence (theft -> running)
4. Suspect class should have highest priority
"""
    
    with open("annotation_guide.md", 'w') as f:
        f.write(annotation_guide)
    
    print("Annotation guide created: annotation_guide.md")

def main():
    """Main function to update dataset structure"""
    
    # Update dataset structure
    new_dataset_dir = update_dataset_structure()
    
    # Create annotation guide
    create_sample_annotations()
    
    # Print statistics
    print("\n=== Dataset Update Summary ===")
    for split in ['train', 'valid', 'test']:
        img_count = len(list((new_dataset_dir / split / 'images').glob('*.jpg')))
        label_count = len(list((new_dataset_dir / split / 'labels').glob('*.txt')))
        print(f"{split}: {img_count} images, {label_count} labels")
    
    print("\n=== Next Steps ===")
    print("1. Review and manually update annotations using annotation_guide.md")
    print("2. Use Roboflow or similar tool to refine annotations")
    print("3. Add more data for 'running' class if needed")
    print("4. Train new model with updated dataset")

if __name__ == "__main__":
    main()
