execution :

import cv2
import numpy as np
from ultralytics import YOLO  

theft_model = YOLO(r"C:\Users\<USER>\Downloads\best (1).pt")
running_model = YOLO(r"C:\Users\<USER>\Downloads\best (2).pt")

theft_detected = False

def detect_theft(frame):
    results = theft_model(frame)
    predictions = results[0].boxes.data.cpu().numpy()
    return predictions

def detect_sudden_running(frame):
    results = running_model(frame)
    predictions = results[0].boxes.data.cpu().numpy()
    return predictions

def draw_predictions(frame, predictions, label, color, confidence_threshold=0.6):
    for pred in predictions:
        x1, y1, x2, y2, confidence, class_id = pred
        if confidence > confidence_threshold:
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
            cv2.putText(frame, f"{label}: {confidence:.2f}", (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    return frame

video_path = r'C:\Users\<USER>\Downloads\second\16.mp4'
output_video_path = r'C:\Users\<USER>\Downloads\output\new output\16.mp4'

cap = cv2.VideoCapture(video_path)
frame_width = int(cap.get(3))
frame_height = int(cap.get(4))
fps = int(cap.get(cv2.CAP_PROP_FPS))

out = cv2.VideoWriter(output_video_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (frame_width, frame_height))

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    theft_predictions = detect_theft(frame)
    frame_with_detections = draw_predictions(frame, theft_predictions, "Theft", (0, 0, 255))

    if any(pred[5] == 0 and pred[4] > 0.6 for pred in theft_predictions):
        theft_detected = True

    if theft_detected:
        running_predictions = detect_sudden_running(frame)
        frame_with_detections = draw_predictions(frame_with_detections, running_predictions, "Sudden Running", (255, 0, 0), 0.3)

        if any(pred[4] > 0.3 for pred in running_predictions):
            print("Run after theft detected")
            break

    out.write(frame_with_detections)

cap.release()
out.release()
cv2.destroyAllWindows()