#!/usr/bin/env python3
"""
Clean training script for three-class theft detection
"""

import os
from ultralytics import YOLO
import yaml

def main():
    print("=== Three-Class Theft Detection Training ===")
    
    # Use the final dataset
    data_path = "final_dataset/data.yaml"
    
    if not os.path.exists(data_path):
        print(f"❌ Dataset not found: {data_path}")
        return
    
    print(f"Using dataset: {data_path}")
    
    # Check dataset
    with open(data_path, 'r') as f:
        data = yaml.safe_load(f)
    
    print(f"Classes: {data.get('names', [])}")
    print(f"Number of classes: {data.get('nc', 0)}")
    
    # Load model
    print("Loading YOLOv8n model...")
    model = YOLO('yolov8n.pt')
    
    # Train with optimized parameters
    try:
        print("🚀 Starting training...")
        results = model.train(
            data=data_path,
            epochs=100,
            imgsz=640,
            batch=8,
            patience=30,
            save=True,
            plots=True,
            device='cpu',
            workers=0,
            verbose=True,
            project='runs/train',
            name='three_class_theft',
            
            # Optimization parameters
            optimizer='AdamW',
            lr0=0.01,
            lrf=0.01,
            momentum=0.937,
            weight_decay=0.0005,
            warmup_epochs=3,
            
            # Loss weights
            box=7.5,
            cls=1.0,
            dfl=1.5,
            
            # Data augmentation
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=10.0,
            translate=0.1,
            scale=0.5,
            fliplr=0.5,
            mosaic=1.0,
            mixup=0.1,
            copy_paste=0.1
        )
        
        print("✅ Training completed!")
        print(f"Results saved to: {results.save_dir}")
        
        # Validate the model
        print("📊 Running validation...")
        val_results = model.val()
        print(f"mAP50: {val_results.box.map50:.4f}")
        print(f"mAP50-95: {val_results.box.map:.4f}")
        
        # Print per-class results
        if hasattr(val_results.box, 'ap_class_index'):
            print("\nPer-class performance:")
            class_names = ['chain_snatch', 'running', 'suspect']
            for i, class_idx in enumerate(val_results.box.ap_class_index):
                if i < len(class_names):
                    class_name = class_names[i]
                    ap50 = val_results.box.ap50[i] if i < len(val_results.box.ap50) else 0
                    ap = val_results.box.ap[i] if i < len(val_results.box.ap) else 0
                    marker = "🚨" if class_name == 'suspect' else "📊"
                    print(f"  {marker} {class_name:12}: mAP50={ap50:.4f}, mAP50-95={ap:.4f}")
        
        # Create deployment script
        best_model = results.save_dir / "weights" / "best.pt"
        if best_model.exists():
            create_deployment_script(str(best_model))
        
        print("\n🎉 Training completed successfully!")
        print("Next steps:")
        print("1. Test: python test_system.py")
        print("2. Detect: python src/advanced_theft_detector.py --video input.mp4 --model runs/train/three_class_theft/weights/best.pt")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

def create_deployment_script(model_path):
    """Create simple deployment script"""
    
    script_content = f'''#!/usr/bin/env python3
"""
Quick deployment for theft detection
"""

import sys
import os
sys.path.append('src')

from advanced_theft_detector import AdvancedTheftDetector

def detect_theft(video_path, output_path=None):
    detector = AdvancedTheftDetector("{model_path}", confidence_threshold=0.5)
    alerts = detector.process_video(video_path, output_path)
    
    if alerts:
        print(f"🚨 {{len(alerts)}} security alerts generated!")
        for alert in alerts:
            print(f"  - {{alert['type']}} at frame {{alert['frame']}}")
    else:
        print("✅ No threats detected")
    
    return alerts

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python deploy.py <video_path> [output_path]")
        sys.exit(1)
    
    video_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    detect_theft(video_path, output_path)
'''
    
    with open("deploy.py", 'w') as f:
        f.write(script_content)
    
    print("📦 Deployment script created: deploy.py")

if __name__ == "__main__":
    main()
